const allowedOrigins = ["http://127.0.0.1:3000", "http://localhost:3000", "http://127.0.0.1:3002", "http://localhost:3002", "http://localhost:5173", "http://localhost:5500", "http://127.0.0.1:9999", "http://localhost:9999"]
const corsOptions = {
    origin: (origin, callback) => {
        if (allowedOrigins.indexOf(origin) !== -1 || !origin) {
            callback(null, true)
        } else {
            callback(new Error("Not allowed by CORS"));
        }
    },
    credentials: true,
    optionsSuccessStatus: 200
}
module.exports = corsOptions;