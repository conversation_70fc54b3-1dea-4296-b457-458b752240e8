# Gimini Flash Backend

A Node.js backend server for the Gimini Flash chatbot with token-based authentication and configurable prompts.

## Features

- 🤖 Chat endpoint with Google Gemini AI integration
- 🔐 JWT-based token authentication (uses tokens from main backend)
- 📝 Configurable system prompts
- 🌐 CORS support for web applications
- 💾 File-based storage for prompts
- 🏥 Health check endpoint

## Setup

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```

3. Copy `.env.example` to `.env` and fill in your values:
   ```bash
   cp .env.example .env
   ```

4. Start the server:
   ```bash
   npm start
   ```

## Environment Variables

- `API_KEY`: Your Google Gemini API key (required)
- `ACCESS_TOKEN_SECRET`: Secret key for JWT token verification (from main backend)
- `SERVER_Port`: Server port (optional, defaults to 3000)
- `DB_USERNAME`, `DB_PASSWORD`, `DB_DATABASE`, `DB_HOST`: Database configuration (from main backend)
- `EXPIERS_IN`: Token expiration time
- `REFRESH_TOKEN_SECRET`: Refresh token secret (from main backend)
- `NEXT_PUBLIC_Url`: Public URL for the application
- `NODE_ENV`: Environment mode (development/production)

## API Endpoints

### Public Endpoints

#### POST /chat
Send a message to the AI chatbot.

**Request:**
```json
{
  "prompt": "Your message here"
}
```

**Response:**
```json
{
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "AI response here"
          }
        ]
      }
    }
  ]
}
```



#### GET /health
Check server health status.

**Response:**
```json
{
  "status": "OK",
  "message": "Server is running",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### Protected Endpoints

All protected endpoints require an `Authorization` header with a Bearer token from your main backend:
```
Authorization: Bearer your_jwt_token_from_main_backend
```

#### GET /prompt
Get the current system prompt.

**Response:**
```json
{
  "prompt": "Current system prompt text..."
}
```

#### PUT /prompt
Update the system prompt.

**Request:**
```json
{
  "prompt": "New system prompt text..."
}
```

**Response:**
```json
{
  "message": "Prompt updated successfully",
  "prompt": "New system prompt text..."
}
```

## File Storage

The application creates the following files for data persistence:

- `prompt.json`: Stores the current system prompt

This file is created automatically when the server starts.

## CORS Configuration

The server is configured to accept requests from:
- http://127.0.0.1:3000
- http://localhost:3000
- http://127.0.0.1:5500
- http://localhost:5500

You can modify the allowed origins in `settings.js`.

## Error Handling

The API returns appropriate HTTP status codes and error messages:

- `400`: Bad Request (missing required fields)
- `401`: Unauthorized (missing token)
- `403`: Forbidden (invalid token)
- `404`: Not Found (endpoint doesn't exist)
- `500`: Internal Server Error

## Development

To run in development mode with auto-restart:
```bash
npm install -g nodemon
nodemon index.js
```
