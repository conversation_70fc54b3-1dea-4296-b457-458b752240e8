# Google Gemini API Key
API_KEY=your_google_gemini_api_key_here

# Database Configuration
DB_USERNAME="Shipment_User"
DB_PASSWORD="Admin@123"
DB_DATABASE="Shipment_app"
DB_HOST="**************"
PORT=1433

# JWT Configuration
EXPIERS_IN="15m"
ACCESS_TOKEN_SECRET='c7992bd0d8c4fad6f6efb9f0d6d5bbbd73be56d5c1e44e70a800525c6a576095'
REFRESH_TOKEN_SECRET='d9a49c7628cc78c0701085d2ec322d26f05219a5b52fb0b4b3abd1d08e12ead6'

# Server Configuration
SERVER_Port="3000"
NEXT_PUBLIC_Url="http://localhost:3000"
NODE_ENV="development"
